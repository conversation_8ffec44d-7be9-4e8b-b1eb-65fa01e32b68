#include "GlobalSearchDialog.h"
#include "../database/dao/ConsumerDao.h"
#include "../database/dao/DepartmentDao.h"
#include "../models/Consumer.h"
#include "../models/Department.h"
#include <QHeaderView>
#include <QKeyEvent>
#include <QMessageBox>
#include <QDebug>
#include <QSqlQuery>
#include <QSqlDatabase>

namespace AccessControl {

GlobalSearchDialog::GlobalSearchDialog(std::shared_ptr<IDatabaseProvider> dbProvider, QWidget *parent)
    : QDialog(parent)
    , m_databaseProvider(dbProvider)
    , m_consumerDao(std::make_unique<ConsumerDao>(dbProvider))
{
    setWindowTitle(tr("全局搜索"));
    setWindowFlags(Qt::Window | Qt::WindowCloseButtonHint);
    setModal(false); // 非模态对话框
    resize(800, 200); // 宽度：800px，高度：200px

    initUI();
    setupConnections();

    // 设置焦点到搜索框
    m_searchEdit->setFocus();
}

GlobalSearchDialog::~GlobalSearchDialog()
{
}

void GlobalSearchDialog::showAndClear()
{
    // 清空搜索框和结果
    m_searchEdit->clear();
    m_resultTable->setRowCount(0);
    m_statusLabel->setText(tr("请输入搜索条件"));

    // 显示对话框并设置焦点
    show();
    raise();
    activateWindow();
    m_searchEdit->setFocus();
}

void GlobalSearchDialog::initUI()
{
    m_mainLayout = new QVBoxLayout(this);

    // 搜索区域
    m_searchLayout = new QHBoxLayout();

    QLabel* searchLabel = new QLabel(tr("搜索:"));
    m_searchEdit = new QLineEdit();
    m_searchEdit->setPlaceholderText(tr("输入任意信息进行搜索（姓名、工号、卡号、手机号、身份证号等）"));
    m_searchButton = new QPushButton(tr("搜索"));
    m_clearButton = new QPushButton(tr("清空"));

    m_searchLayout->addWidget(searchLabel);
    m_searchLayout->addWidget(m_searchEdit, 1);
    m_searchLayout->addWidget(m_searchButton);
    m_searchLayout->addWidget(m_clearButton);

    // 结果表格
    m_resultTable = new QTableWidget();
    QStringList headers;
    headers << tr("工号") << tr("姓名") << tr("卡号") << tr("部门") << tr("手机号") << tr("身份证号");
    m_resultTable->setColumnCount(headers.size());
    m_resultTable->setHorizontalHeaderLabels(headers);

    // 设置表格属性
    m_resultTable->setAlternatingRowColors(true);
    m_resultTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_resultTable->setSelectionMode(QAbstractItemView::SingleSelection);
    m_resultTable->setSortingEnabled(true);
    m_resultTable->horizontalHeader()->setStretchLastSection(true);

    // 设置列宽
    m_resultTable->setColumnWidth(0, 100);  // 工号
    m_resultTable->setColumnWidth(1, 100);  // 姓名
    m_resultTable->setColumnWidth(2, 120);  // 卡号
    m_resultTable->setColumnWidth(3, 150);  // 部门
    m_resultTable->setColumnWidth(4, 120);  // 手机号
    m_resultTable->setColumnWidth(5, 150);  // 身份证号

    // 状态标签
    m_statusLabel = new QLabel(tr("请输入搜索条件"));

    // 按钮区域
    m_buttonLayout = new QHBoxLayout();
    m_closeButton = new QPushButton(tr("关闭"));
    m_buttonLayout->addStretch();
    m_buttonLayout->addWidget(m_closeButton);

    // 添加到主布局
    m_mainLayout->addLayout(m_searchLayout);
    m_mainLayout->addWidget(m_resultTable, 1);
    m_mainLayout->addWidget(m_statusLabel);
    m_mainLayout->addLayout(m_buttonLayout);
}

void GlobalSearchDialog::setupConnections()
{
    connect(m_searchEdit, &QLineEdit::returnPressed, this, &GlobalSearchDialog::performSearch);
    connect(m_searchEdit, &QLineEdit::textChanged, this, &GlobalSearchDialog::onSearchTextChanged);
    connect(m_searchButton, &QPushButton::clicked, this, &GlobalSearchDialog::performSearch);
    connect(m_clearButton, &QPushButton::clicked, this, &GlobalSearchDialog::clearSearch);
    connect(m_closeButton, &QPushButton::clicked, this, &QDialog::close);
    connect(m_resultTable, &QTableWidget::cellDoubleClicked, this, &GlobalSearchDialog::onTableDoubleClicked);
}

void GlobalSearchDialog::performSearch()
{
    QString searchText = m_searchEdit->text().trimmed();
    if (searchText.isEmpty()) {
        m_statusLabel->setText(tr("请输入搜索条件"));
        m_resultTable->setRowCount(0);
        return;
    }

    // 清空之前的搜索结果
    m_resultTable->setRowCount(0);

    try {
        qDebug() << "GlobalSearchDialog::performSearch - Searching for:" << searchText;

        // 先测试获取所有用户，检查数据库连接
        auto allConsumers = m_consumerDao->getAllConsumers();
        qDebug() << "GlobalSearchDialog::performSearch - Total consumers in database:" << allConsumers.size();

        // 使用searchConsumers方法进行全局搜索
        auto consumers = m_consumerDao->searchConsumers(searchText);

        qDebug() << "GlobalSearchDialog::performSearch - Found" << consumers.size() << "consumers";

        populateResults(consumers);

        if (consumers.empty()) {
            m_statusLabel->setText(tr("未找到匹配的用户（数据库中共有 %1 个用户）").arg(allConsumers.size()));
            qDebug() << "GlobalSearchDialog::performSearch - No consumers found";
        } else {
            m_statusLabel->setText(tr("找到 %1 个匹配的用户").arg(consumers.size()));
            qDebug() << "GlobalSearchDialog::performSearch - Displaying" << consumers.size() << "consumers";
        }
    } catch (const std::exception& e) {
        qDebug() << "Global search failed:" << e.what();
        m_statusLabel->setText(tr("搜索失败"));
        QMessageBox::critical(this, tr("错误"), tr("搜索时发生错误：%1").arg(e.what()));
    }
}

void GlobalSearchDialog::clearSearch()
{
    m_searchEdit->clear();
    m_resultTable->setRowCount(0);
    m_statusLabel->setText(tr("请输入搜索条件"));
    m_searchEdit->setFocus();
}

void GlobalSearchDialog::onTableDoubleClicked(int row, int column)
{
    Q_UNUSED(column)

    if (row >= 0 && row < m_resultTable->rowCount()) {
        // 获取用户ID（存储在第一列的UserRole中）
        QTableWidgetItem* item = m_resultTable->item(row, 0);
        if (item) {
            int consumerId = item->data(Qt::UserRole).toInt();
            emit consumerSelected(consumerId);
            close();
        }
    }
}

void GlobalSearchDialog::onSearchTextChanged()
{
    // 可以在这里实现实时搜索，现在先留空
}

void GlobalSearchDialog::populateResults(const std::vector<Consumer>& consumers)
{
    m_resultTable->setRowCount(0);
    m_resultTable->setRowCount(consumers.size());

    for (size_t i = 0; i < consumers.size(); ++i) {
        fillConsumerRow(i, consumers[i]);
    }
}

void GlobalSearchDialog::fillConsumerRow(int row, const Consumer& consumer)
{
    // 工号
    QTableWidgetItem* workNumberItem = new QTableWidgetItem(consumer.workNumber());
    workNumberItem->setData(Qt::UserRole, consumer.id()); // 存储用户ID
    m_resultTable->setItem(row, 0, workNumberItem);

    // 姓名
    m_resultTable->setItem(row, 1, new QTableWidgetItem(consumer.getDisplayName()));

    // 卡号
    QString cardNumber = getPrimaryCardNumber(consumer.id());
    m_resultTable->setItem(row, 2, new QTableWidgetItem(cardNumber));

    // 部门
    QString departmentName = getDepartmentName(consumer.departmentId());
    m_resultTable->setItem(row, 3, new QTableWidgetItem(departmentName));

    // 手机号
    m_resultTable->setItem(row, 4, new QTableWidgetItem(consumer.phoneNumber()));

    // 身份证号
    m_resultTable->setItem(row, 5, new QTableWidgetItem(consumer.idNumber()));
}

QString GlobalSearchDialog::getDepartmentName(int departmentId)
{
    if (departmentId <= 0) {
        return tr("未分配");
    }

    try {
        DepartmentDao departmentDao(m_databaseProvider);
        Department dept = departmentDao.findById(departmentId);
        if (dept.id() > 0) {
            QString fullPath = dept.fullPath();
            if (!fullPath.isEmpty()) {
                return fullPath.replace('/', '\\');
            }
            return dept.name();
        }
    } catch (const std::exception& e) {
        qDebug() << "Failed to get department name:" << e.what();
    }

    return tr("未知部门");
}

QString GlobalSearchDialog::getPrimaryCardNumber(int consumerId)
{
    try {
        QSqlDatabase db = m_databaseProvider->getDatabase();
        QSqlQuery query(db);
        query.prepare("SELECT card_number FROM user_cards WHERE user_id = ? AND is_primary = 1 LIMIT 1");
        query.addBindValue(consumerId);

        if (query.exec() && query.next()) {
            return query.value(0).toString();
        }
    } catch (const std::exception& e) {
        qDebug() << "Failed to get primary card number:" << e.what();
    }

    return QString();
}

void GlobalSearchDialog::keyPressEvent(QKeyEvent *event)
{
    if (event->key() == Qt::Key_Escape) {
        close();
    } else {
        QDialog::keyPressEvent(event);
    }
}

} // namespace AccessControl
