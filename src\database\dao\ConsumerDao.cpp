#include "ConsumerDao.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QVariant>
#include <QDebug>
#include <QDateTime>

namespace AccessControl {

ConsumerDao::ConsumerDao(DatabaseProviderPtr provider)
    : m_provider(provider)
{
}

bool ConsumerDao::create(Consumer& consumer)
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    query.prepare(
        "INSERT INTO consumers (work_number, real_name, phone_number, id_number, "
        "department_id, status, access_enabled, attendance_enabled, shift_work, "
        "valid_from, valid_until, created_at, updated_at) "
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
    );

    bindConsumerToQuery(query, consumer);

    if (!query.exec()) {
        qDebug() << "ConsumerDao::create - Failed to create consumer:" << query.lastError().text();
        return false;
    }

    consumer.setId(query.lastInsertId().toInt());
    return true;
}

std::optional<Consumer> ConsumerDao::getById(int id)
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    query.prepare(
        "SELECT id, work_number, real_name, phone_number, id_number, "
        "department_id, status, access_enabled, attendance_enabled, shift_work, "
        "valid_from, valid_until, created_at, updated_at "
        "FROM consumers WHERE id = ?"
    );
    query.addBindValue(id);

    if (!query.exec() || !query.next()) {
        qDebug() << "ConsumerDao::getById - Failed to get consumer by ID:" << query.lastError().text();
        return std::nullopt;
    }

    return mapRowToConsumer(query);
}

std::optional<Consumer> ConsumerDao::getByWorkNumber(const QString& workNumber)
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    query.prepare(
        "SELECT id, work_number, real_name, phone_number, id_number, "
        "department_id, status, access_enabled, attendance_enabled, shift_work, "
        "valid_from, valid_until, created_at, updated_at "
        "FROM consumers WHERE work_number = ?"
    );
    query.addBindValue(workNumber);

    if (!query.exec() || !query.next()) {
        qDebug() << "ConsumerDao::getByWorkNumber - Failed to get consumer by work number:" << query.lastError().text();
        return std::nullopt;
    }

    return mapRowToConsumer(query);
}

bool ConsumerDao::update(const Consumer& consumer)
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    query.prepare(
        "UPDATE consumers SET "
        "work_number = ?, real_name = ?, phone_number = ?, id_number = ?, "
        "department_id = ?, status = ?, access_enabled = ?, attendance_enabled = ?, "
        "shift_work = ?, valid_from = ?, valid_until = ?, updated_at = ? "
        "WHERE id = ?"
    );

    bindConsumerToQuery(query, consumer, true);
    query.addBindValue(consumer.id());

    if (!query.exec()) {
        qDebug() << "ConsumerDao::update - Failed to update consumer:" << query.lastError().text();
        return false;
    }

    return query.numRowsAffected() > 0;
}

bool ConsumerDao::remove(int id)
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    query.prepare("DELETE FROM consumers WHERE id = ?");
    query.addBindValue(id);

    if (!query.exec()) {
        qDebug() << "ConsumerDao::remove - Failed to remove consumer:" << query.lastError().text();
        return false;
    }

    return query.numRowsAffected() > 0;
}

std::vector<Consumer> ConsumerDao::getAllConsumers()
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    query.prepare(
        "SELECT id, work_number, real_name, phone_number, id_number, "
        "department_id, status, access_enabled, attendance_enabled, shift_work, "
        "valid_from, valid_until, created_at, updated_at "
        "FROM consumers ORDER BY id"
    );

    std::vector<Consumer> consumers;

    if (!query.exec()) {
        qDebug() << "ConsumerDao::getAllConsumers - Failed to get all consumers:" << query.lastError().text();
        return consumers;
    }

    while (query.next()) {
        consumers.push_back(mapRowToConsumer(query));
    }

    return consumers;
}

std::vector<Consumer> ConsumerDao::getConsumersByDepartment(int departmentId)
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    query.prepare(
        "SELECT id, work_number, real_name, phone_number, id_number, "
        "department_id, status, access_enabled, attendance_enabled, shift_work, "
        "valid_from, valid_until, created_at, updated_at "
        "FROM consumers WHERE department_id = ? ORDER BY id"
    );
    query.addBindValue(departmentId);

    std::vector<Consumer> consumers;

    if (!query.exec()) {
        qDebug() << "ConsumerDao::getConsumersByDepartment - Failed to get consumers by department:" << query.lastError().text();
        return consumers;
    }

    while (query.next()) {
        consumers.push_back(mapRowToConsumer(query));
    }

    return consumers;
}

std::vector<Consumer> ConsumerDao::searchConsumers(const QString& searchTerm)
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    query.prepare(
        "SELECT DISTINCT c.id, c.work_number, c.real_name, c.phone_number, c.id_number, "
        "c.department_id, c.status, c.access_enabled, c.attendance_enabled, c.shift_work, "
        "c.valid_from, c.valid_until, c.created_at, c.updated_at "
        "FROM consumers c "
        "LEFT JOIN user_cards uc ON c.id = uc.user_id "
        "WHERE c.work_number LIKE ? OR c.real_name LIKE ? OR c.phone_number LIKE ? OR c.id_number LIKE ? OR uc.card_number LIKE ? "
        "ORDER BY c.id"
    );

    QString term = "%" + searchTerm + "%";
    query.addBindValue(term);
    query.addBindValue(term);
    query.addBindValue(term);
    query.addBindValue(term);
    query.addBindValue(term);

    std::vector<Consumer> consumers;

    if (!query.exec()) {
        qDebug() << "ConsumerDao::searchConsumers - Failed to search consumers:" << query.lastError().text();
        return consumers;
    }

    while (query.next()) {
        consumers.push_back(mapRowToConsumer(query));
    }

    return consumers;
}

std::vector<Consumer> ConsumerDao::searchConsumersByName(const QString& name)
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    query.prepare(
        "SELECT id, work_number, real_name, phone_number, id_number, "
        "department_id, status, access_enabled, attendance_enabled, shift_work, "
        "valid_from, valid_until, created_at, updated_at "
        "FROM consumers WHERE real_name LIKE ? ORDER BY id"
    );

    query.addBindValue("%" + name + "%");

    std::vector<Consumer> consumers;

    if (!query.exec()) {
        qDebug() << "ConsumerDao::searchConsumersByName - Failed to search consumers by name:" << query.lastError().text();
        return consumers;
    }

    while (query.next()) {
        consumers.push_back(mapRowToConsumer(query));
    }

    return consumers;
}

std::vector<Consumer> ConsumerDao::searchConsumersByCard(const QString& cardNumber)
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    query.prepare(
        "SELECT c.id, c.work_number, c.real_name, c.phone_number, c.id_number, "
        "c.department_id, c.status, c.access_enabled, c.attendance_enabled, c.shift_work, "
        "c.valid_from, c.valid_until, c.created_at, c.updated_at "
        "FROM consumers c "
        "JOIN user_cards uc ON c.id = uc.user_id "
        "WHERE uc.card_number LIKE ? ORDER BY c.id"
    );

    query.addBindValue("%" + cardNumber + "%");

    std::vector<Consumer> consumers;

    if (!query.exec()) {
        qDebug() << "ConsumerDao::searchConsumersByCard - Failed to search consumers by card:" << query.lastError().text();
        return consumers;
    }

    while (query.next()) {
        consumers.push_back(mapRowToConsumer(query));
    }

    return consumers;
}

std::vector<Consumer> ConsumerDao::getConsumersWithAdvancedFilter(const QString& name, const QString& cardNumber, int departmentId)
{
    QSqlDatabase db = m_provider->getDatabase();

    QString sql =
        "SELECT DISTINCT c.id, c.work_number, c.real_name, c.phone_number, c.id_number, "
        "c.department_id, c.status, c.access_enabled, c.attendance_enabled, c.shift_work, "
        "c.valid_from, c.valid_until, c.created_at, c.updated_at "
        "FROM consumers c ";

    // 如果有工号/卡号筛选，需要关联user_cards表
    if (!cardNumber.isEmpty()) {
        sql += "LEFT JOIN user_cards uc ON c.id = uc.user_id ";
    }

    // 构建WHERE子句
    QStringList conditions;
    if (!name.isEmpty()) {
        conditions << "(c.real_name LIKE :name)";
    }
    if (!cardNumber.isEmpty()) {
        // 修改为同时查询工号和卡号
        conditions << "(c.work_number LIKE :worknum OR uc.card_number LIKE :card)";
    }
    if (departmentId > 0) {
        conditions << "(c.department_id = :dept)";
    }

    if (!conditions.isEmpty()) {
        sql += "WHERE " + conditions.join(" AND ");
    }

    sql += " ORDER BY c.id";

    QSqlQuery query(db);
    query.prepare(sql);

    if (!name.isEmpty()) {
        query.bindValue(":name", "%" + name + "%");
    }
    if (!cardNumber.isEmpty()) {
        query.bindValue(":worknum", "%" + cardNumber + "%");
        query.bindValue(":card", "%" + cardNumber + "%");
    }
    if (departmentId > 0) {
        query.bindValue(":dept", departmentId);
    }

    std::vector<Consumer> consumers;

    if (!query.exec()) {
        qDebug() << "ConsumerDao::getConsumersWithAdvancedFilter - Failed to filter consumers:" << query.lastError().text();
        return consumers;
    }

    while (query.next()) {
        consumers.push_back(mapRowToConsumer(query));
    }

    return consumers;
}

bool ConsumerDao::isWorkNumberExists(const QString& workNumber, int excludeId)
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    if (excludeId > 0) {
        query.prepare("SELECT COUNT(*) FROM consumers WHERE work_number = ? AND id != ?");
        query.addBindValue(workNumber);
        query.addBindValue(excludeId);
    } else {
        query.prepare("SELECT COUNT(*) FROM consumers WHERE work_number = ?");
        query.addBindValue(workNumber);
    }

    if (!query.exec() || !query.next()) {
        qDebug() << "ConsumerDao::isWorkNumberExists - Failed to check work number:" << query.lastError().text();
        return false;
    }

    return query.value(0).toInt() > 0;
}

bool ConsumerDao::isPhoneNumberExists(const QString& phoneNumber, int excludeId)
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    if (excludeId > 0) {
        query.prepare("SELECT COUNT(*) FROM consumers WHERE phone_number = ? AND id != ?");
        query.addBindValue(phoneNumber);
        query.addBindValue(excludeId);
    } else {
        query.prepare("SELECT COUNT(*) FROM consumers WHERE phone_number = ?");
        query.addBindValue(phoneNumber);
    }

    if (!query.exec() || !query.next()) {
        qDebug() << "ConsumerDao::isPhoneNumberExists - Failed to check phone number:" << query.lastError().text();
        return false;
    }

    return query.value(0).toInt() > 0;
}

bool ConsumerDao::isIdNumberExists(const QString& idNumber, int excludeId)
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    if (excludeId > 0) {
        query.prepare("SELECT COUNT(*) FROM consumers WHERE id_number = ? AND id != ?");
        query.addBindValue(idNumber);
        query.addBindValue(excludeId);
    } else {
        query.prepare("SELECT COUNT(*) FROM consumers WHERE id_number = ?");
        query.addBindValue(idNumber);
    }

    if (!query.exec() || !query.next()) {
        qDebug() << "ConsumerDao::isIdNumberExists - Failed to check ID number:" << query.lastError().text();
        return false;
    }

    return query.value(0).toInt() > 0;
}

int ConsumerDao::getTotalConsumerCount()
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    query.prepare("SELECT COUNT(*) FROM consumers");

    if (!query.exec() || !query.next()) {
        qDebug() << "ConsumerDao::getTotalConsumerCount - Failed to get count:" << query.lastError().text();
        return 0;
    }

    return query.value(0).toInt();
}

int ConsumerDao::getActiveConsumerCount()
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    query.prepare("SELECT COUNT(*) FROM consumers WHERE status = 0"); // 0 = Active

    if (!query.exec() || !query.next()) {
        qDebug() << "ConsumerDao::getActiveConsumerCount - Failed to get active count:" << query.lastError().text();
        return 0;
    }

    return query.value(0).toInt();
}

int ConsumerDao::getConsumerCountByDepartment(int departmentId)
{
    QSqlDatabase db = m_provider->getDatabase();

    QSqlQuery query(db);
    query.prepare("SELECT COUNT(*) FROM consumers WHERE department_id = ?");
    query.addBindValue(departmentId);

    if (!query.exec() || !query.next()) {
        qDebug() << "ConsumerDao::getConsumerCountByDepartment - Failed to get count by department:" << query.lastError().text();
        return 0;
    }

    return query.value(0).toInt();
}

bool ConsumerDao::batchCreate(const std::vector<Consumer>& consumers)
{
    QSqlDatabase db = m_provider->getDatabase();

    bool success = true;
    db.transaction();

    for (const auto& consumer : consumers) {
        QSqlQuery query(db);
        query.prepare(
            "INSERT INTO consumers (work_number, real_name, phone_number, id_number, "
            "department_id, status, access_enabled, attendance_enabled, shift_work, "
            "valid_from, valid_until, created_at, updated_at) "
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
        );

        bindConsumerToQuery(query, consumer);

        if (!query.exec()) {
            qDebug() << "ConsumerDao::batchCreate - Failed to create consumer:" << query.lastError().text();
            success = false;
            break;
        }
    }

    if (success) {
        db.commit();
    } else {
        db.rollback();
    }

    return success;
}

bool ConsumerDao::batchUpdate(const std::vector<Consumer>& consumers)
{
    QSqlDatabase db = m_provider->getDatabase();

    bool success = true;
    db.transaction();

    for (const auto& consumer : consumers) {
        QSqlQuery query(db);
        query.prepare(
            "UPDATE consumers SET "
            "work_number = ?, real_name = ?, phone_number = ?, id_number = ?, "
            "department_id = ?, status = ?, access_enabled = ?, attendance_enabled = ?, "
            "shift_work = ?, valid_from = ?, valid_until = ?, updated_at = ? "
            "WHERE id = ?"
        );

        bindConsumerToQuery(query, consumer);
        query.addBindValue(consumer.id());

        if (!query.exec()) {
            qDebug() << "ConsumerDao::batchUpdate - Failed to update consumer:" << query.lastError().text();
            success = false;
            break;
        }
    }

    if (success) {
        db.commit();
    } else {
        db.rollback();
    }

    return success;
}

bool ConsumerDao::batchDelete(const std::vector<int>& consumerIds)
{
    QSqlDatabase db = m_provider->getDatabase();

    bool success = true;
    db.transaction();

    for (int id : consumerIds) {
        QSqlQuery query(db);
        query.prepare("DELETE FROM consumers WHERE id = ?");
        query.addBindValue(id);

        if (!query.exec()) {
            qDebug() << "ConsumerDao::batchDelete - Failed to delete consumer:" << query.lastError().text();
            success = false;
            break;
        }
    }

    if (success) {
        db.commit();
    } else {
        db.rollback();
    }

    return success;
}

Consumer ConsumerDao::mapRowToConsumer(const QSqlQuery& query)
{
    Consumer consumer;

    consumer.setId(query.value("id").toInt());
    consumer.setWorkNumber(query.value("work_number").toString());
    consumer.setRealName(query.value("real_name").toString());
    consumer.setPhoneNumber(query.value("phone_number").toString());
    consumer.setIdNumber(query.value("id_number").toString());
    consumer.setDepartmentId(query.value("department_id").toInt());
    consumer.setStatus(static_cast<Consumer::Status>(query.value("status").toInt()));
    consumer.setAccessEnabled(query.value("access_enabled").toBool());
    consumer.setAttendanceEnabled(query.value("attendance_enabled").toBool());
    consumer.setShiftWork(query.value("shift_work").toBool());
    consumer.setValidFrom(query.value("valid_from").toDate());
    consumer.setValidUntil(query.value("valid_until").toDate());
    consumer.setCreatedAt(query.value("created_at").toDateTime());
    consumer.setUpdatedAt(query.value("updated_at").toDateTime());

    return consumer;
}

void ConsumerDao::bindConsumerToQuery(QSqlQuery& query, const Consumer& consumer, bool isUpdate)
{
    query.addBindValue(consumer.workNumber());
    query.addBindValue(consumer.realName());
    query.addBindValue(consumer.phoneNumber());
    query.addBindValue(consumer.idNumber());

    // 处理部门ID：如果为-1，则设置为NULL
    if (consumer.departmentId() == -1) {
        query.addBindValue(QVariant());
    } else {
        query.addBindValue(consumer.departmentId());
    }

    query.addBindValue(static_cast<int>(consumer.status()));
    query.addBindValue(consumer.accessEnabled());
    query.addBindValue(consumer.attendanceEnabled());
    query.addBindValue(consumer.shiftWork());
    query.addBindValue(consumer.validFrom());
    query.addBindValue(consumer.validUntil());

    if (!isUpdate) {
        // 创建时需要添加创建时间和更新时间
        query.addBindValue(QDateTime::currentDateTime()); // created_at
        query.addBindValue(QDateTime::currentDateTime()); // updated_at
    } else {
        // 更新时只需要添加更新时间
        query.addBindValue(QDateTime::currentDateTime()); // updated_at
    }
}

} // namespace AccessControl