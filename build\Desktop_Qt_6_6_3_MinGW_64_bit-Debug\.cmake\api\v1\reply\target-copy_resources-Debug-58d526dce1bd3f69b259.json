{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 114, "parent": 0}]}, "id": "copy_resources::@6890427a1f51a3e7e1df", "name": "copy_resources", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/CMakeFiles/copy_resources", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/CMakeFiles/copy_resources.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}