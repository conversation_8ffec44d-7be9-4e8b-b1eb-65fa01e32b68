/****************************************************************************
** Meta object code from reading C++ file 'ConsumerManagementWidget.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/views/ConsumerManagementWidget.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ConsumerManagementWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEConsumerManagementWidgetENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAccessControlSCOPEConsumerManagementWidgetENDCLASS = QtMocHelpers::stringData(
    "AccessControl::ConsumerManagementWidget",
    "loadConsumers",
    "",
    "refreshConsumerTable",
    "onBatchAdd",
    "onAdd",
    "onEdit",
    "onDelete",
    "onPrint",
    "onExport",
    "onImport",
    "onLost",
    "onSearch",
    "onFilter",
    "onClearFilter",
    "onNameSearchChanged",
    "onCardSearchChanged",
    "onDeptSearchChanged",
    "onTableDoubleClicked",
    "row",
    "column",
    "onTableSelectionChanged",
    "onGlobalSearch",
    "onConsumerSelectedFromSearch",
    "consumerId"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEConsumerManagementWidgetENDCLASS_t {
    uint offsetsAndSizes[50];
    char stringdata0[40];
    char stringdata1[14];
    char stringdata2[1];
    char stringdata3[21];
    char stringdata4[11];
    char stringdata5[6];
    char stringdata6[7];
    char stringdata7[9];
    char stringdata8[8];
    char stringdata9[9];
    char stringdata10[9];
    char stringdata11[7];
    char stringdata12[9];
    char stringdata13[9];
    char stringdata14[14];
    char stringdata15[20];
    char stringdata16[20];
    char stringdata17[20];
    char stringdata18[21];
    char stringdata19[4];
    char stringdata20[7];
    char stringdata21[24];
    char stringdata22[15];
    char stringdata23[29];
    char stringdata24[11];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSAccessControlSCOPEConsumerManagementWidgetENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSAccessControlSCOPEConsumerManagementWidgetENDCLASS_t qt_meta_stringdata_CLASSAccessControlSCOPEConsumerManagementWidgetENDCLASS = {
    {
        QT_MOC_LITERAL(0, 39),  // "AccessControl::ConsumerManage..."
        QT_MOC_LITERAL(40, 13),  // "loadConsumers"
        QT_MOC_LITERAL(54, 0),  // ""
        QT_MOC_LITERAL(55, 20),  // "refreshConsumerTable"
        QT_MOC_LITERAL(76, 10),  // "onBatchAdd"
        QT_MOC_LITERAL(87, 5),  // "onAdd"
        QT_MOC_LITERAL(93, 6),  // "onEdit"
        QT_MOC_LITERAL(100, 8),  // "onDelete"
        QT_MOC_LITERAL(109, 7),  // "onPrint"
        QT_MOC_LITERAL(117, 8),  // "onExport"
        QT_MOC_LITERAL(126, 8),  // "onImport"
        QT_MOC_LITERAL(135, 6),  // "onLost"
        QT_MOC_LITERAL(142, 8),  // "onSearch"
        QT_MOC_LITERAL(151, 8),  // "onFilter"
        QT_MOC_LITERAL(160, 13),  // "onClearFilter"
        QT_MOC_LITERAL(174, 19),  // "onNameSearchChanged"
        QT_MOC_LITERAL(194, 19),  // "onCardSearchChanged"
        QT_MOC_LITERAL(214, 19),  // "onDeptSearchChanged"
        QT_MOC_LITERAL(234, 20),  // "onTableDoubleClicked"
        QT_MOC_LITERAL(255, 3),  // "row"
        QT_MOC_LITERAL(259, 6),  // "column"
        QT_MOC_LITERAL(266, 23),  // "onTableSelectionChanged"
        QT_MOC_LITERAL(290, 14),  // "onGlobalSearch"
        QT_MOC_LITERAL(305, 28),  // "onConsumerSelectedFromSearch"
        QT_MOC_LITERAL(334, 10)   // "consumerId"
    },
    "AccessControl::ConsumerManagementWidget",
    "loadConsumers",
    "",
    "refreshConsumerTable",
    "onBatchAdd",
    "onAdd",
    "onEdit",
    "onDelete",
    "onPrint",
    "onExport",
    "onImport",
    "onLost",
    "onSearch",
    "onFilter",
    "onClearFilter",
    "onNameSearchChanged",
    "onCardSearchChanged",
    "onDeptSearchChanged",
    "onTableDoubleClicked",
    "row",
    "column",
    "onTableSelectionChanged",
    "onGlobalSearch",
    "onConsumerSelectedFromSearch",
    "consumerId"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAccessControlSCOPEConsumerManagementWidgetENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
      20,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  134,    2, 0x08,    1 /* Private */,
       3,    0,  135,    2, 0x08,    2 /* Private */,
       4,    0,  136,    2, 0x08,    3 /* Private */,
       5,    0,  137,    2, 0x08,    4 /* Private */,
       6,    0,  138,    2, 0x08,    5 /* Private */,
       7,    0,  139,    2, 0x08,    6 /* Private */,
       8,    0,  140,    2, 0x08,    7 /* Private */,
       9,    0,  141,    2, 0x08,    8 /* Private */,
      10,    0,  142,    2, 0x08,    9 /* Private */,
      11,    0,  143,    2, 0x08,   10 /* Private */,
      12,    0,  144,    2, 0x08,   11 /* Private */,
      13,    0,  145,    2, 0x08,   12 /* Private */,
      14,    0,  146,    2, 0x08,   13 /* Private */,
      15,    0,  147,    2, 0x08,   14 /* Private */,
      16,    0,  148,    2, 0x08,   15 /* Private */,
      17,    0,  149,    2, 0x08,   16 /* Private */,
      18,    2,  150,    2, 0x08,   17 /* Private */,
      21,    0,  155,    2, 0x08,   20 /* Private */,
      22,    0,  156,    2, 0x08,   21 /* Private */,
      23,    1,  157,    2, 0x08,   22 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,   19,   20,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   24,

       0        // eod
};

Q_CONSTINIT const QMetaObject AccessControl::ConsumerManagementWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_CLASSAccessControlSCOPEConsumerManagementWidgetENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAccessControlSCOPEConsumerManagementWidgetENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSAccessControlSCOPEConsumerManagementWidgetENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ConsumerManagementWidget, std::true_type>,
        // method 'loadConsumers'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'refreshConsumerTable'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onBatchAdd'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAdd'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onEdit'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDelete'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onPrint'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onExport'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onImport'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onLost'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSearch'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onFilter'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onClearFilter'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onNameSearchChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onCardSearchChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDeptSearchChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onTableDoubleClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onTableSelectionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onGlobalSearch'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onConsumerSelectedFromSearch'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>
    >,
    nullptr
} };

void AccessControl::ConsumerManagementWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ConsumerManagementWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->loadConsumers(); break;
        case 1: _t->refreshConsumerTable(); break;
        case 2: _t->onBatchAdd(); break;
        case 3: _t->onAdd(); break;
        case 4: _t->onEdit(); break;
        case 5: _t->onDelete(); break;
        case 6: _t->onPrint(); break;
        case 7: _t->onExport(); break;
        case 8: _t->onImport(); break;
        case 9: _t->onLost(); break;
        case 10: _t->onSearch(); break;
        case 11: _t->onFilter(); break;
        case 12: _t->onClearFilter(); break;
        case 13: _t->onNameSearchChanged(); break;
        case 14: _t->onCardSearchChanged(); break;
        case 15: _t->onDeptSearchChanged(); break;
        case 16: _t->onTableDoubleClicked((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 17: _t->onTableSelectionChanged(); break;
        case 18: _t->onGlobalSearch(); break;
        case 19: _t->onConsumerSelectedFromSearch((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject *AccessControl::ConsumerManagementWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AccessControl::ConsumerManagementWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSAccessControlSCOPEConsumerManagementWidgetENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int AccessControl::ConsumerManagementWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 20)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 20;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 20)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 20;
    }
    return _id;
}
QT_WARNING_POP
