<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ConsumerManagementWidget</class>
 <widget class="QWidget" name="ConsumerManagementWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>门禁卡持有者管理</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QFrame" name="buttonFrame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QPushButton" name="batchAddButton">
        <property name="text">
         <string>批量添加</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="addButton">
        <property name="text">
         <string>添加</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="editButton">
        <property name="text">
         <string>修改</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="deleteButton">
        <property name="text">
         <string>删除</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="printButton">
        <property name="text">
         <string>打印</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="exportButton">
        <property name="text">
         <string>导出Excel</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="importButton">
        <property name="text">
         <string>导入用户</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="lostButton">
        <property name="text">
         <string>挂失</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="searchButton">
        <property name="text">
         <string>查找</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="searchFrame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QLabel" name="nameLabel">
        <property name="text">
         <string>姓名:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="nameSearchEdit">
        <property name="placeholderText">
         <string>输入姓名</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="cardLabel">
        <property name="text">
         <string>工号/卡号:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="cardSearchEdit">
        <property name="placeholderText">
         <string>输入工号或卡号</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="deptLabel">
        <property name="text">
         <string>部门:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="deptSearchCombo"/>
      </item>
      <item>
       <widget class="QPushButton" name="filterButton">
        <property name="text">
         <string>查询</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="clearFilterButton">
        <property name="text">
         <string>清空条件</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QTableWidget" name="consumerTableWidget">
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectRows</enum>
     </property>
     <property name="sortingEnabled">
      <bool>true</bool>
     </property>
     <attribute name="horizontalHeaderStretchLastSection">
      <bool>true</bool>
     </attribute>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="statusFrame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <item>
       <widget class="QLabel" name="totalLabel">
        <property name="text">
         <string>总计：0 条记录</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="statusLabel">
        <property name="text">
         <string>就绪</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>