#ifndef CONSUMERMANAGEMENTWIDGET_H
#define CONSUMERMANAGEMENTWIDGET_H

#include <QWidget>
#include <QTableWidget>
#include <QLineEdit>
#include <QComboBox>
#include <QPushButton>
#include <QLabel>
#include <QFrame>
#include <QMessageBox>
#include <QFileDialog>
#include <QShortcut>
#include <memory>
#include <vector>
#include "../models/Consumer.h"
#include "../database/dao/ConsumerDao.h"

namespace Ui {
class ConsumerManagementWidget;
}

namespace AccessControl {

class IDatabaseProvider;
class DepartmentDao;
class GlobalSearchDialog;

/**
 * @brief 门禁卡持有者管理界面
 * 用于管理门禁卡持有者信息，与系统用户管理(UserManagementWidget)区分开
 */
class ConsumerManagementWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ConsumerManagementWidget(std::shared_ptr<IDatabaseProvider> dbProvider, QWidget *parent = nullptr);
    ~ConsumerManagementWidget();

private slots:
    // 数据操作
    void loadConsumers();
    void refreshConsumerTable();

    // 功能按钮
    void onBatchAdd();
    void onAdd();
    void onEdit();
    void onDelete();
    void onPrint();
    void onExport();
    void onImport();
    void onLost();
    void onSearch();

    // 搜索筛选
    void onFilter();
    void onClearFilter();
    void onNameSearchChanged();
    void onCardSearchChanged();
    void onDeptSearchChanged();

    // 表格操作
    void onTableDoubleClicked(int row, int column);
    void onTableSelectionChanged();

    // 全局搜索
    void onGlobalSearch();
    void onConsumerSelectedFromSearch(int consumerId);

    // 导入导出功能
    void onExportExcel();
    void onImportUsers();

private:
    void setupUi();
    void setupConnections();
    void setupTableHeaders();
    void loadDepartments();
    void updateStatusBar();
    void updateButtonStates();

    // 数据显示
    void populateConsumerTable(const std::vector<Consumer>& consumers);
    void fillConsumerRow(int row, const Consumer& consumer);
    QString getDepartmentName(int departmentId);
    QString formatDate(const QDateTime& dateTime);
    QString formatBoolValue(bool value);
    QString formatIdNumber(const QString& idNumber);
    QString getPrimaryCardNumber(int consumerId);

    // 搜索过滤
    std::vector<Consumer> filterConsumers(const std::vector<Consumer>& consumers);
    bool matchesSearchCriteria(const Consumer& consumer);

    // 获取选中用户
    std::vector<Consumer> getSelectedConsumers();
    Consumer* getCurrentConsumer();

    Ui::ConsumerManagementWidget *ui;
    std::shared_ptr<IDatabaseProvider> m_databaseProvider;
    std::unique_ptr<ConsumerDao> m_consumerDao;
    std::unique_ptr<DepartmentDao> m_departmentDao;

    // 数据缓存
    std::vector<Consumer> m_allConsumers;
    std::vector<Consumer> m_filteredConsumers;

    // 界面元素
    QTableWidget* m_consumerTable;
    QLineEdit* m_nameSearchEdit;
    QLineEdit* m_cardSearchEdit;
    QComboBox* m_deptSearchCombo;
    QLabel* m_totalLabel;
    QLabel* m_statusLabel;

    // 按钮组
    QPushButton* m_batchAddButton;
    QPushButton* m_addButton;
    QPushButton* m_editButton;
    QPushButton* m_deleteButton;
    QPushButton* m_printButton;
    QPushButton* m_exportButton;
    QPushButton* m_importButton;
    QPushButton* m_lostButton;
    QPushButton* m_searchButton;
    QPushButton* m_filterButton;
    QPushButton* m_clearFilterButton;

    // 全局搜索相关
    GlobalSearchDialog* m_globalSearchDialog;
    QShortcut* m_searchShortcut;
};

} // namespace AccessControl

#endif // CONSUMERMANAGEMENTWIDGET_H