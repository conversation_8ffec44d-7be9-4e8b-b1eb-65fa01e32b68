{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-12de70d265c141917d0d.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "AccessControlSystem", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "AccessControlSystem::@6890427a1f51a3e7e1df", "jsonFile": "target-AccessControlSystem-Debug-2a4684e7ec572449eb43.json", "name": "AccessControlSystem", "projectIndex": 0}, {"directoryIndex": 0, "id": "AccessControlSystem_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-AccessControlSystem_autogen-Debug-600205faf76284cd7340.json", "name": "AccessControlSystem_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "AccessControlSystem_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-AccessControlSystem_autogen_timestamp_deps-Debug-423d36a6ed96272e2fe7.json", "name": "AccessControlSystem_autogen_timestamp_deps", "projectIndex": 0}, {"directoryIndex": 0, "id": "copy_resources::@6890427a1f51a3e7e1df", "jsonFile": "target-copy_resources-Debug-58d526dce1bd3f69b259.json", "name": "copy_resources", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug", "source": "C:/Users/<USER>/Documents/AccessControlSystem"}, "version": {"major": 2, "minor": 7}}