#include "ConsumerCardDialog.h"
#include "CardLineEdit.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QHeaderView>
#include <QTableWidgetItem>
#include <QMessageBox>
#include <QRegularExpression>
#include <QLabel>

namespace AccessControl {

ConsumerCardDialog::ConsumerCardDialog(QWidget *parent)
    : QDialog(parent)
    , m_cardNumberEdit(nullptr)
    , m_cardTypeCombo(nullptr)
    , m_isPrimaryCardCheck(nullptr)
    , m_addCardButton(nullptr)
    , m_editCardButton(nullptr)
    , m_deleteCardButton(nullptr)
    , m_cardTable(nullptr)
    , m_okButton(nullptr)
    , m_cancelButton(nullptr)
{
    setWindowTitle(tr("卡片管理"));
    setMinimumSize(600, 500);
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);

    initUI();
}

ConsumerCardDialog::~ConsumerCardDialog()
{
    // 析构函数内容
}

void ConsumerCardDialog::initUI()
{
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    // 卡片输入区域
    QGroupBox *inputGroup = new QGroupBox(tr("添加卡片"), this);
    QFormLayout *inputLayout = new QFormLayout(inputGroup);

    // 卡号输入 - 使用CardLineEdit支持USB读卡器
    m_cardNumberEdit = new CardLineEdit(inputGroup);
    m_cardNumberEdit->setPlaceholderText(tr("请刷卡或输入卡号"));
    m_cardNumberEdit->setMaxLength(32);

    // 处理多次刷卡，只保留最后一次刷卡的卡号
    connect(m_cardNumberEdit, &QLineEdit::textChanged, this, [this](const QString &text) {
        // 防止递归调用
        static bool isProcessing = false;
        if (isProcessing) return;

        isProcessing = true;

        // 处理多行输入（刷卡器输入可能包含换行符）
        QStringList lines = text.split(QRegularExpression("[\r\n]"), Qt::SkipEmptyParts);
        if (!lines.isEmpty()) {
            QString latestCard = lines.last().trimmed();
            // 只有当显示的文本与最新卡号不一致时才更新
            if (m_cardNumberEdit->text() != latestCard) {
                m_cardNumberEdit->setText(latestCard);
            }
        }

        // 调用验证函数
        validateCardNumber();

        isProcessing = false;
    });

    inputLayout->addRow(tr("卡号:"), m_cardNumberEdit);

    // 卡类型
    m_cardTypeCombo = new QComboBox(inputGroup);
    m_cardTypeCombo->addItem(tr("IC/ID卡"), 0);
    m_cardTypeCombo->addItem(tr("CPU卡"), 1);
    m_cardTypeCombo->addItem(tr("手机APP"), 2);
    m_cardTypeCombo->addItem(tr("手机NFC"), 3);
    m_cardTypeCombo->addItem(tr("指纹"), 4);
    m_cardTypeCombo->addItem(tr("人脸"), 5);
    m_cardTypeCombo->addItem(tr("手机号"), 6);
    m_cardTypeCombo->addItem(tr("身份证"), 7);
    inputLayout->addRow(tr("卡类型:"), m_cardTypeCombo);

    // 是否主卡
    m_isPrimaryCardCheck = new QCheckBox(tr("设为主卡"), inputGroup);
    inputLayout->addRow(tr(""), m_isPrimaryCardCheck);

    // 按钮区域
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    m_addCardButton = new QPushButton(tr("添加卡片"), inputGroup);
    m_editCardButton = new QPushButton(tr("编辑卡片"), inputGroup);
    m_deleteCardButton = new QPushButton(tr("删除卡片"), inputGroup);

    connect(m_addCardButton, &QPushButton::clicked, this, &ConsumerCardDialog::addCard);
    connect(m_editCardButton, &QPushButton::clicked, this, &ConsumerCardDialog::editCard);
    connect(m_deleteCardButton, &QPushButton::clicked, this, &ConsumerCardDialog::deleteCard);

    buttonLayout->addWidget(m_addCardButton);
    buttonLayout->addWidget(m_editCardButton);
    buttonLayout->addWidget(m_deleteCardButton);
    buttonLayout->addStretch();

    inputLayout->addRow(tr(""), buttonLayout);

    // 卡片列表
    m_cardTable = new QTableWidget(0, 4, this);
    QStringList headers;
    headers << tr("卡号") << tr("卡类型") << tr("是否主卡") << tr("状态");
    m_cardTable->setHorizontalHeaderLabels(headers);
    m_cardTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_cardTable->setSelectionMode(QAbstractItemView::SingleSelection);
    m_cardTable->horizontalHeader()->setStretchLastSection(true);

    // 对话框按钮
    QHBoxLayout *dialogButtonLayout = new QHBoxLayout();
    dialogButtonLayout->addStretch();

    m_okButton = new QPushButton(tr("确定"), this);
    m_cancelButton = new QPushButton(tr("取消"), this);

    connect(m_okButton, &QPushButton::clicked, this, &ConsumerCardDialog::accept);
    connect(m_cancelButton, &QPushButton::clicked, this, &QDialog::reject);

    dialogButtonLayout->addWidget(m_okButton);
    dialogButtonLayout->addWidget(m_cancelButton);

    // 添加到主布局
    mainLayout->addWidget(inputGroup);
    mainLayout->addWidget(new QLabel(tr("已添加的卡片:"), this));
    mainLayout->addWidget(m_cardTable);
    mainLayout->addLayout(dialogButtonLayout);

    // 初始状态设置
    m_addCardButton->setEnabled(false);
    m_editCardButton->setEnabled(false);
    m_deleteCardButton->setEnabled(false);

    // 连接表格选择信号
    connect(m_cardTable, &QTableWidget::itemSelectionChanged, this, [this]() {
        bool hasSelection = m_cardTable->currentRow() >= 0;
        m_editCardButton->setEnabled(hasSelection);
        m_deleteCardButton->setEnabled(hasSelection);
    });
}

QList<QStringList> ConsumerCardDialog::getCardData() const
{
    QList<QStringList> cardData;
    for (int i = 0; i < m_cardTable->rowCount(); ++i) {
        QStringList row;
        for (int j = 0; j < m_cardTable->columnCount(); ++j) {
            row << m_cardTable->item(i, j)->text();
        }
        cardData << row;
    }
    return cardData;
}

void ConsumerCardDialog::setCardData(const QList<QStringList>& cardData)
{
    m_cardTable->setRowCount(0);
    for (const QStringList& row : cardData) {
        int tableRow = m_cardTable->rowCount();
        m_cardTable->insertRow(tableRow);
        for (int j = 0; j < row.size() && j < m_cardTable->columnCount(); ++j) {
            m_cardTable->setItem(tableRow, j, new QTableWidgetItem(row[j]));
        }
    }
}

void ConsumerCardDialog::addCard()
{
    QString cardNumber = formatCardNumber(m_cardNumberEdit->text());
    if (cardNumber.isEmpty()) {
        QMessageBox::warning(this, tr("警告"), tr("请输入正确的卡号！"));
        m_cardNumberEdit->setFocus();
        return;
    }

    // 检查卡号是否重复
    for (int i = 0; i < m_cardTable->rowCount(); ++i) {
        if (m_cardTable->item(i, 0)->text() == cardNumber) {
            QMessageBox::warning(this, tr("警告"), tr("卡号已存在！"));
            m_cardNumberEdit->setFocus();
            return;
        }
    }

    // 添加卡片到表格
    int row = m_cardTable->rowCount();
    m_cardTable->insertRow(row);

    m_cardTable->setItem(row, 0, new QTableWidgetItem(cardNumber));
    m_cardTable->setItem(row, 1, new QTableWidgetItem(m_cardTypeCombo->currentText()));
    m_cardTable->setItem(row, 2, new QTableWidgetItem(m_isPrimaryCardCheck->isChecked() ? tr("是") : tr("否")));
    m_cardTable->setItem(row, 3, new QTableWidgetItem(tr("正常")));

    // 如果设为主卡，将其他卡片的主卡状态取消
    if (m_isPrimaryCardCheck->isChecked()) {
        for (int i = 0; i < m_cardTable->rowCount() - 1; ++i) {
            m_cardTable->setItem(i, 2, new QTableWidgetItem(tr("否")));
        }
    }

    // 清空输入
    m_cardNumberEdit->clear();
    m_isPrimaryCardCheck->setChecked(false);

    QMessageBox::information(this, tr("成功"), tr("卡片添加成功！"));
}

void ConsumerCardDialog::editCard()
{
    int currentRow = m_cardTable->currentRow();
    if (currentRow < 0) {
        QMessageBox::warning(this, tr("警告"), tr("请选择要编辑的卡片！"));
        return;
    }

    // 将选中的卡片信息填入输入框
    m_cardNumberEdit->setText(m_cardTable->item(currentRow, 0)->text());

    QString cardType = m_cardTable->item(currentRow, 1)->text();
    for (int i = 0; i < m_cardTypeCombo->count(); ++i) {
        if (m_cardTypeCombo->itemText(i) == cardType) {
            m_cardTypeCombo->setCurrentIndex(i);
            break;
        }
    }

    m_isPrimaryCardCheck->setChecked(m_cardTable->item(currentRow, 2)->text() == tr("是"));

    // 删除原记录，等待重新添加
    m_cardTable->removeRow(currentRow);
}

void ConsumerCardDialog::deleteCard()
{
    int currentRow = m_cardTable->currentRow();
    if (currentRow < 0) {
        QMessageBox::warning(this, tr("警告"), tr("请选择要删除的卡片！"));
        return;
    }

    if (QMessageBox::question(this, tr("确认删除"), tr("确定要删除选中的卡片吗？"),
                             QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes) {
        m_cardTable->removeRow(currentRow);
        QMessageBox::information(this, tr("成功"), tr("卡片删除成功！"));
    }
}

void ConsumerCardDialog::validateCardNumber()
{
    QString text = m_cardNumberEdit->text();

    // 只允许数字输入
    QRegularExpression regex("\\d*");
    if (!regex.match(text).hasMatch() || regex.match(text).captured(0) != text) {
        // 移除非数字字符
        text = text.remove(QRegularExpression("[^\\d]"));
        m_cardNumberEdit->setText(text);
    }

    // 验证范围
    if (!text.isEmpty()) {
        bool ok;
        qulonglong number = text.toULongLong(&ok);
        if (!ok || number == 0 || number > 1152921504606846975ULL) {
            m_cardNumberEdit->setStyleSheet("QLineEdit { border: 2px solid red; }");
            m_addCardButton->setEnabled(false);
        } else {
            m_cardNumberEdit->setStyleSheet("");
            m_addCardButton->setEnabled(true);
        }
    } else {
        m_cardNumberEdit->setStyleSheet("");
        m_addCardButton->setEnabled(false);
    }
}

QString ConsumerCardDialog::formatCardNumber(const QString& cardNumber)
{
    QString trimmed = cardNumber.trimmed();

    // 移除前导零
    while (trimmed.startsWith("0") && trimmed.length() > 1) {
        trimmed = trimmed.mid(1);
    }

    // 验证范围
    if (trimmed.isEmpty()) {
        return QString();
    }

    bool ok;
    qulonglong number = trimmed.toULongLong(&ok);
    if (!ok || number == 0 || number > 1152921504606846975ULL) {
        return QString();
    }

    return trimmed;
}

void ConsumerCardDialog::accept()
{
    // 可以在这里添加额外的验证
    QDialog::accept();
}

} // namespace AccessControl