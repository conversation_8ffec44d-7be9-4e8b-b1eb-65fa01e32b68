#ifndef CONSUMERDIALOG_H
#define CONSUMERDIALOG_H

#include <QDialog>
#include <QTabWidget>
#include <QLineEdit>
#include <QPushButton>
#include <QCheckBox>
#include <QComboBox>
#include <QDateEdit>
#include <QSpinBox>
#include <QTextEdit>
#include <QTableWidget>
#include <QList>
#include <memory>

#include "../models/Department.h"

QT_BEGIN_NAMESPACE
class QFormLayout;
class QVBoxLayout;
class QHBoxLayout;
QT_END_NAMESPACE

// 前向声明
class CardLineEdit;

namespace AccessControl {

class Consumer;
class ConsumerPhotoWidget;
class ConsumerFingerprintWidget;
class IDatabaseProvider;
class DepartmentDao;
class ConsumerDao;

/**
 * @brief 简单的添加卡片对话框
 */
class AddCardDialog : public QDialog
{
    Q_OBJECT

public:
    explicit AddCardDialog(QWidget *parent = nullptr, bool isFirstCard = false);

    QString getCardNumber() const;
    QString getCardType() const;
    bool isPrimaryCard() const;

    // 设置当前值的方法（用于编辑模式）
    void setCardNumber(const QString& cardNumber);
    void setCardType(const QString& cardType);
    void setPrimaryCard(bool isPrimary);

private slots:
    void validateCardNumber();

private:
    void initUI();
    QString formatCardNumber(const QString& cardNumber) const;

private:
    QLineEdit* m_cardNumberEdit;
    QComboBox* m_cardTypeCombo;
    QCheckBox* m_isPrimaryCardCheck;
    QPushButton* m_okButton;
    QPushButton* m_cancelButton;
    QString m_lastCardNumber;
};

/**
 * @brief 门禁用户对话框
 *
 * 用于添加和编辑门禁用户信息的对话框
 */
class ConsumerDialog : public QDialog
{
    Q_OBJECT

public:
    /**
     * @brief 对话框模式
     */
    enum class Mode {
        Add,    ///< 添加模式
        Edit    ///< 编辑模式
    };

    /**
     * @brief 构造函数
     * @param dbProvider 数据库提供者
     * @param parent 父窗口
     * @param mode 对话框模式
     * @param consumerId 用户ID（编辑模式时使用）
     */
    explicit ConsumerDialog(std::shared_ptr<IDatabaseProvider> dbProvider,
                           QWidget *parent = nullptr, Mode mode = Mode::Add, int consumerId = -1);

    /**
     * @brief 析构函数
     */
    ~ConsumerDialog();

protected:
    /**
     * @brief 关闭事件处理
     * @param event 关闭事件
     */
    void closeEvent(QCloseEvent *event) override;

private slots:
    /**
     * @brief 保存用户信息
     */
    void saveUser();

    /**
     * @brief 取消操作
     */
    void cancel();

    /**
     * @brief 添加卡片
     */
    void addCard();

    void editCard();
    void deleteCard();

private:
    /**
     * @brief 初始化UI
     */
    void initUI();

    /**
     * @brief 创建基本信息标签页
     */
    QWidget* createBasicInfoTab();

    /**
     * @brief 创建扩展信息标签页
     */
    QWidget* createExtendedInfoTab();

    /**
     * @brief 创建指纹管理标签页
     */
    QWidget* createFingerprintTab();

    /**
     * @brief 填充用户数据（编辑模式）
     */
    void fillConsumerData();

    /**
     * @brief 验证表单数据
     * @return 验证结果
     */
    bool validateForm();

    /**
     * @brief 保存用户数据到数据库
     * @return 保存结果
     */
    bool saveConsumerToDatabase();

    /**
     * @brief 自动生成工号
     * @param workNumbers 现有工号列表
     */
    void autoGenerateWorkNumber(const QStringList &workNumbers);

    /**
     * @brief 获取所有工号
     * @return 工号列表
     */
    QStringList getAllWorkNumbers();

    /**
     * @brief 获取当前日期时间字符串
     * @return 格式化的日期时间
     */
    QString getCurrentDateTime();

    /**
     * @brief 获取照片文件名
     * @return 照片文件名（不含扩展名）
     */
    QString getPhotoFileName() const;

    /**
     * @brief 更新照片文件名（当卡片变更时调用）
     */
    void updatePhotoFileName();

    /**
     * @brief 加载用户卡片信息
     * @param consumerId 用户ID
     */
    void loadConsumerCards(int consumerId);

    /**
     * @brief 加载用户照片
     */
    void loadConsumerPhoto();

    /**
     * @brief 获取卡片类型名称
     * @param cardType 卡片类型编号
     * @return 卡片类型名称
     */
    QString getCardTypeName(int cardType);

    /**
     * @brief 获取卡片状态名称
     * @param status 状态编号
     * @return 状态名称
     */
    QString getCardStatusName(int status);

    /**
     * @brief 保存卡片数据到数据库
     * @param consumerId 用户ID
     * @return 保存是否成功
     */
    bool saveCardsToDatabase(int consumerId);

    /**
     * @brief 获取卡片类型索引
     * @param cardType 卡片类型名称
     * @return 卡片类型索引
     */
    int getCardTypeIndex(const QString& cardType);

    /**
     * @brief 根据卡类型查找卡号
     * @param cardType 卡类型
     * @return 卡号，未找到返回空字符串
     */
    QString findCardNumberByType(const QString& cardType) const;

    /**
     * @brief 获取主卡卡号
     * @return 主卡卡号，未找到返回空字符串
     */
    QString getPrimaryCardNumber() const;

    /**
     * @brief 加载部门数据到下拉框
     */
    void loadDepartments();

    /**
     * @brief 构建部门完整路径
     * @param dept 部门对象
     * @param allDepartments 所有部门列表
     * @return 完整路径字符串
     */
    QString buildDepartmentPath(const Department& dept, const QList<Department>& allDepartments) const;

    /**
     * @brief 验证手机号格式
     * @param phoneNumber 手机号
     * @return 验证结果
     */
    bool validatePhoneNumber(const QString& phoneNumber) const;

    /**
     * @brief 验证身份证号格式
     * @param idNumber 身份证号
     * @return 验证结果
     */
    bool validateIdNumber(const QString& idNumber) const;



private:
    Mode m_mode;
    int m_userId;

    // 数据库相关
    std::shared_ptr<IDatabaseProvider> m_dbProvider;
    std::unique_ptr<DepartmentDao> m_departmentDao;
    std::unique_ptr<ConsumerDao> m_consumerDao;

    // UI控件
    QTabWidget* m_tabWidget;

    // 基本信息控件
    QLineEdit* m_workNumberEdit;         // 工号
    QLineEdit* m_nameEdit;               // 姓名
    QLineEdit* m_phoneEdit;              // 手机号
    QLineEdit* m_idNumberEdit;           // 身份证号
    QComboBox* m_departmentCombo;        // 部门
    QCheckBox* m_attendanceCheck;        // 考勤启用
    QCheckBox* m_accessCheck;            // 门禁启用
    QCheckBox* m_shiftWorkCheck;         // 倒班
    QDateEdit* m_validFromEdit;          // 起始日期
    QDateEdit* m_validUntilEdit;         // 截止日期
    QComboBox* m_statusCombo;            // 用户状态

    // 扩展信息控件
    QComboBox* m_genderCombo;            // 性别
    QLineEdit* m_workPhoneEdit;          // 工作电话
    QLineEdit* m_homePhoneEdit;          // 家庭电话
    QLineEdit* m_nationEdit;             // 民族
    QLineEdit* m_religionEdit;           // 宗教
    QLineEdit* m_birthplaceEdit;         // 籍贯
    QDateEdit* m_birthDateEdit;          // 出生年月
    QComboBox* m_maritalCombo;           // 婚姻状态
    QLineEdit* m_politicalEdit;          // 政治面貌
    QComboBox* m_educationCombo;         // 学历
    QLineEdit* m_englishNameEdit;        // 英文名
    QLineEdit* m_companyEdit;            // 单位
    QLineEdit* m_jobTitleEdit;           // 职称
    QLineEdit* m_technicalLevelEdit;     // 技术等级
    QLineEdit* m_certificateNameEdit;    // 证件名称
    QLineEdit* m_certificateNumberEdit;  // 证件号
    QLineEdit* m_socialSecurityEdit;     // 社保号
    QDateEdit* m_entryDateEdit;          // 入职时间
    QDateEdit* m_leaveDateEdit;          // 离职时间
    QLineEdit* m_emailEdit;              // 电子邮箱
    QLineEdit* m_addressEdit;            // 通讯地址
    QLineEdit* m_postalCodeEdit;         // 邮编
    QTextEdit* m_remarksEdit;            // 备注

    // 卡片管理控件
    QTableWidget* m_cardTable;           // 卡片列表
    QPushButton* m_addCardButton;        // 添加卡片按钮

    // 功能控件
    ConsumerPhotoWidget* m_photoWidget;
    ConsumerFingerprintWidget* m_fingerprintWidget;

    // 按钮控件
    QPushButton* m_saveButton;
    QPushButton* m_cancelButton;
};

} // namespace AccessControl

#endif // CONSUMERDIALOG_H